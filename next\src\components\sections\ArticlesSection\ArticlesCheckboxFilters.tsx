import { SingleSelection } from '@react-types/shared'
import { useQuery } from '@tanstack/react-query'
import React, { ReactNode, useState } from 'react'
import { Checkbox, CheckboxGroup, CheckboxProps, Label } from 'react-aria-components'

import SelectField, { SelectItem } from '@/src/components/common/SelectField/SelectField'
import { client } from '@/src/services/graphql/gql'
import { ArticlesFilters } from '@/src/services/meili/fetchers/articlesFetcher'
import cn from '@/src/utils/cn'
import { isDefined } from '@/src/utils/isDefined'
import { useLocale } from '@/src/utils/useLocale'
import { useTranslation } from '@/src/utils/useTranslation'

type Props = {
  filters: ArticlesFilters
  onFiltersChange: (filters: ArticlesFilters) => void
}

/**
 * Figma: https://www.figma.com/design/17wbd0MDQcMW9NbXl6UPs8/DS--Component-library?node-id=16846-35571&m=dev
 */

const ArticlesCheckboxFilters = ({ filters, onFiltersChange }: Props) => {
  const { t } = useTranslation()
  const locale = useLocale()

  const { data: articleCategories } = useQuery({
    queryKey: ['ArticleCategories', locale],
    queryFn: () => client.ArticleCategories({ locale }),
    staleTime: Infinity,
    select: (res) => res.articleCategories.filter(isDefined) ?? [],
  })

  const articleCategoriesSelectItems = [
    {
      title: t('ArticlesFilterGroup.allArticleCategories'),
      slug: 'all',
    },
    ...(articleCategories ?? []),
  ]

  const { data: tags } = useQuery({
    queryKey: ['Tags', locale],
    queryFn: () => client.Tags({ locale }),
    staleTime: Infinity,
    select: (res) => res.tags.filter(isDefined) ?? [],
  })

  const tagsSelectItems = [
    {
      title: t('ArticlesFilterGroup.allTags'),
      slug: 'all',
    },
    ...(tags ?? []),
  ]

  const { data: adminGroups } = useQuery({
    queryKey: ['AdminGroups'],
    queryFn: () => client.AdminGroups(),
    staleTime: Infinity,
    select: (res) => res.adminGroups.filter(isDefined),
  })

  // "City Hall" is a special option that means articles without any assigned admin group
  // It is not present in the admin groups list in strapi, so we add it here manually
  const CITY_HALL_SELECT_ITEM = {
    title: t('ArticlesFilterGroup.cityHall'),
    slug: t('ArticlesFilterGroup.cityHall'),
  }

  const adminGroupsSelectItems = [
    {
      title: t('ArticlesFilterGroup.allAdminGroups'),
      slug: 'all',
    },
    CITY_HALL_SELECT_ITEM,
    ...(adminGroups ?? []),
  ]

  const handleCategoryChange = (selectedCategory: SingleSelection['selectedKey']) => {
    onFiltersChange({
      ...filters,
      articleCategorySlugs:
        typeof selectedCategory === 'string' && selectedCategory !== 'all'
          ? [selectedCategory]
          : [],
      page: 1,
    })
  }

  // const handleTagChange = (selectedTag: SingleSelection['selectedKey']) => {
  //   onFiltersChange({
  //     ...filters,
  //     tagSlugs: typeof selectedTag === 'string' && selectedTag !== 'all' ? [selectedTag] : [],
  //     page: 1,
  //   })
  // }

  const handleTagsChange = (selectedTags: string[]) => {
    onFiltersChange({
      ...filters,
      tagSlugs: selectedTags,
      page: 1,
    })
  }

  const handleAuthorChange = (selectedAdminGroup: SingleSelection['selectedKey']) => {
    if (selectedAdminGroup === CITY_HALL_SELECT_ITEM.slug) {
      onFiltersChange({
        ...filters,
        adminGroupSlugs: [],
        excludeArticlesWithAssignedAdminGroups: true,
        page: 1,
      })

      return
    }

    onFiltersChange({
      ...filters,
      adminGroupSlugs:
        typeof selectedAdminGroup === 'string' && selectedAdminGroup !== 'all'
          ? [selectedAdminGroup]
          : [],
      excludeArticlesWithAssignedAdminGroups: false,
      page: 1,
    })
  }

  return (
    <div className="flex flex-col gap-6">
      <CheckboxGroup
        className="flex flex-col gap-2"
        value={filters.tagSlugs}
        onChange={(val) => {
          handleTagsChange(val)
        }}
      >
        {/* eslint-disable-next-line i18next/no-literal-string */}
        <Label className="font-semibold">Favorite sports</Label>
        <div className="flex flex-col gap-3 rounded-lg border border-border-passive-primary px-4 py-5">
          {tags?.map((tag) => {
            return (
              <Checkbox
                // TODO focus ring now travels through every checkbox on tab - can we make this skippable?
                key={tag.documentId}
                isSelected={filters.tagSlugs && filters.tagSlugs.includes(tag.slug)}
                value={tag.slug}
                className={({ isSelected, isHovered, isDisabled, isIndeterminate }) => {
                  return cn('base-focus-ring flex items-center gap-3 rounded-sm', {
                    '**:data-checkbox-rectangle:bg-amber-100': isSelected,
                  })
                }}
              >
                <div
                  data-checkbox-rectangle
                  className={cn(
                    'size-6 rounded-md border-[0.2rem] ',
                  )}
                  aria-hidden
                />
                {tag.title}
              </Checkbox>
            )
          })}
        </div>
      </CheckboxGroup>
    </div>
  )
}

export default ArticlesCheckboxFilters
